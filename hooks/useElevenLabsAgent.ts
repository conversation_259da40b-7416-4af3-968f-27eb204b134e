// hooks/useElevenLabsAgent.ts
import { useState, useEffect, useRef, useCallback } from 'react'

interface ElevenLabsConfig {
  agentId: string
  apiKey?: string
  onTranscript?: (transcript: string) => void
  onAgentResponse?: (response: string) => void
  onCommand?: (command: any) => void
  onError?: (error: Error) => void
  debug?: boolean
}

interface AgentState {
  isConnected: boolean
  isListening: boolean
  isSpeaking: boolean
  connectionStatus: 'idle' | 'connecting' | 'connected' | 'error' | 'disconnected'
  transcript: string
  agentResponse: string
  error: Error | null
}

export function useElevenLabsAgent(config: ElevenLabsConfig) {
  const [state, setState] = useState<AgentState>({
    isConnected: false,
    isListening: false,
    isSpeaking: false,
    connectionStatus: 'idle',
    transcript: '',
    agentResponse: '',
    error: null
  })

  const websocketRef = useRef<WebSocket | null>(null)
  const audioContextRef = useRef<AudioContext | null>(null)
  const audioQueueRef = useRef<AudioBuffer[]>([])
  const isPlayingRef = useRef(false)

  // Initialize WebSocket connection
  const connect = useCallback(async () => {
    setState(prev => ({ ...prev, connectionStatus: 'connecting', error: null }))

    try {
      // Initialize audio context
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()

      // Connect to Eleven Labs WebSocket
      const wsUrl = `wss://api.elevenlabs.io/v1/convai/conversation?agent_id=${config.agentId}`
      const ws = new WebSocket(wsUrl, {
        headers: {
          'xi-api-key': config.apiKey || process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || ''
        }
      } as any)

      ws.onopen = () => {
        if (config.debug) console.log('🔗 WebSocket connected')
        setState(prev => ({ 
          ...prev, 
          isConnected: true, 
          connectionStatus: 'connected',
          isListening: true 
        }))
      }

      ws.onmessage = async (event) => {
        try {
          const data = JSON.parse(event.data)
          
          switch (data.type) {
            case 'audio':
              // Handle incoming audio from agent
              if (data.audio) {
                const audioBuffer = await decodeAudioData(data.audio)
                audioQueueRef.current.push(audioBuffer)
                playNextAudio()
              }
              break

            case 'transcript':
              // User's speech transcribed
              setState(prev => ({ ...prev, transcript: data.text }))
              if (config.onTranscript) {
                config.onTranscript(data.text)
              }
              // Parse for commands
              parseVoiceCommand(data.text)
              break

            case 'agent_response':
              // Agent's text response
              setState(prev => ({ ...prev, agentResponse: data.text }))
              if (config.onAgentResponse) {
                config.onAgentResponse(data.text)
              }
              break

            case 'function_call':
              // Agent wants to execute a function
              if (config.onCommand) {
                config.onCommand(data.function)
              }
              break

            case 'conversation_end':
              disconnect()
              break

            default:
              if (config.debug) console.log('Unknown message type:', data.type)
          }
        } catch (error) {
          console.error('Error processing message:', error)
        }
      }

      ws.onerror = (error) => {
        console.error('WebSocket error:', error)
        setState(prev => ({ 
          ...prev, 
          error: new Error('Connection error'),
          connectionStatus: 'error' 
        }))
        if (config.onError) {
          config.onError(new Error('WebSocket connection error'))
        }
      }

      ws.onclose = () => {
        if (config.debug) console.log('🔌 WebSocket disconnected')
        setState(prev => ({ 
          ...prev, 
          isConnected: false,
          isListening: false,
          connectionStatus: 'disconnected' 
        }))
      }

      websocketRef.current = ws

      // Start capturing audio
      await startAudioCapture()

    } catch (error) {
      console.error('Connection error:', error)
      setState(prev => ({ 
        ...prev, 
        error: error as Error,
        connectionStatus: 'error' 
      }))
      if (config.onError) {
        config.onError(error as Error)
      }
    }
  }, [config])

  // Disconnect from agent
  const disconnect = useCallback(() => {
    if (websocketRef.current) {
      websocketRef.current.close()
      websocketRef.current = null
    }
    
    if (audioContextRef.current) {
      audioContextRef.current.close()
      audioContextRef.current = null
    }

    setState(prev => ({
      ...prev,
      isConnected: false,
      isListening: false,
      isSpeaking: false,
      connectionStatus: 'disconnected'
    }))
  }, [])

  // Start capturing user's audio
  const startAudioCapture = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      })

      mediaRecorder.ondataavailable = async (event) => {
        if (event.data.size > 0 && websocketRef.current?.readyState === WebSocket.OPEN) {
          // Convert blob to base64 and send
          const reader = new FileReader()
          reader.onloadend = () => {
            const base64 = reader.result?.toString().split(',')[1]
            websocketRef.current?.send(JSON.stringify({
              type: 'audio_input',
              audio: base64
            }))
          }
          reader.readAsDataURL(event.data)
        }
      }

      // Send audio chunks every 100ms
      mediaRecorder.start(100)

    } catch (error) {
      console.error('Error accessing microphone:', error)
      setState(prev => ({ 
        ...prev, 
        error: new Error('Microphone access denied') 
      }))
    }
  }

  // Decode base64 audio to AudioBuffer
  const decodeAudioData = async (base64Audio: string): Promise<AudioBuffer> => {
    const binaryString = atob(base64Audio)
    const bytes = new Uint8Array(binaryString.length)
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i)
    }
    
    const audioBuffer = await audioContextRef.current!.decodeAudioData(bytes.buffer)
    return audioBuffer
  }

  // Play queued audio
  const playNextAudio = () => {
    if (isPlayingRef.current || audioQueueRef.current.length === 0) return

    isPlayingRef.current = true
    setState(prev => ({ ...prev, isSpeaking: true }))

    const audioBuffer = audioQueueRef.current.shift()!
    const source = audioContextRef.current!.createBufferSource()
    source.buffer = audioBuffer
    source.connect(audioContextRef.current!.destination)

    source.onended = () => {
      isPlayingRef.current = false
      if (audioQueueRef.current.length > 0) {
        playNextAudio()
      } else {
        setState(prev => ({ ...prev, isSpeaking: false }))
      }
    }

    source.start()
  }

  // Parse voice commands for quote items
  const parseVoiceCommand = (text: string) => {
    const lowerText = text.toLowerCase()
    
    // Gate commands
    const gateMatch = lowerText.match(/(single|double|sliding)?\s*gate.*?(\d+(?:\.\d+)?)\s*(?:meter|metre|m)/i)
    if (gateMatch) {
      const type = gateMatch[1] || 'single'
      const width = parseFloat(gateMatch[2])
      
      config.onCommand?.({
        action: 'addQuoteItem',
        params: {
          type: 'gate',
          description: `${type.charAt(0).toUpperCase() + type.slice(1)} Gate`,
          material: type,
          width,
          quantity: 1
        }
      })
    }

    // Fence commands
    const fenceMatch = lowerText.match(/(\d+(?:\.\d+)?)\s*(?:meter|metre|m).*?(paling|picket|colorbond)?\s*fenc/i)
    if (fenceMatch) {
      const length = parseFloat(fenceMatch[1])
      const material = fenceMatch[2] || 'paling'
      
      config.onCommand?.({
        action: 'addQuoteItem',
        params: {
          type: 'fence',
          description: `${material.charAt(0).toUpperCase() + material.slice(1)} Fence`,
          material,
          length,
          height: 1.8,
          quantity: 1
        }
      })
    }

    // Automation commands
    if (lowerText.includes('automat') || lowerText.includes('solar') || lowerText.includes('electric')) {
      const isDouble = lowerText.includes('double')
      const isSolar = lowerText.includes('solar')
      
      config.onCommand?.({
        action: 'addQuoteItem',
        params: {
          type: 'automation',
          description: `${isDouble ? 'Double' : 'Single'} ${isSolar ? 'Solar' : 'Electric'} Automation`,
          material: `${isDouble ? 'double' : 'single'} ${isSolar ? 'solar' : 'electric'}`,
          quantity: 1
        }
      })
    }

    // Labor commands
    const laborMatch = lowerText.match(/(\d+)\s*hour/i)
    if (laborMatch && lowerText.includes('labor')) {
      const hours = parseInt(laborMatch[1])
      
      config.onCommand?.({
        action: 'updateLaborCost',
        params: {
          hours,
          includeCallOut: true
        }
      })
    }

    // Customer info commands
    if (lowerText.includes('customer') || lowerText.includes('name is') || lowerText.includes('email')) {
      // Extract email
      const emailMatch = text.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/)
      if (emailMatch) {
        config.onCommand?.({
          action: 'updateCustomerInfo',
          params: { email: emailMatch[0] }
        })
      }

      // Extract phone
      const phoneMatch = text.match(/\b(?:\+?61|0)4\d{8}\b/)
      if (phoneMatch) {
        config.onCommand?.({
          action: 'updateCustomerInfo',
          params: { phone: phoneMatch[0] }
        })
      }
    }
  }

  // Send text message to agent
  const sendMessage = useCallback((message: string) => {
    if (websocketRef.current?.readyState === WebSocket.OPEN) {
      websocketRef.current.send(JSON.stringify({
        type: 'text_input',
        text: message
      }))
    }
  }, [])

  // Toggle listening
  const toggleListening = useCallback(() => {
    if (websocketRef.current?.readyState === WebSocket.OPEN) {
      const newState = !state.isListening
      websocketRef.current.send(JSON.stringify({
        type: 'toggle_listening',
        enabled: newState
      }))
      setState(prev => ({ ...prev, isListening: newState }))
    }
  }, [state.isListening])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  return {
    // State
    ...state,
    
    // Actions
    connect,
    disconnect,
    sendMessage,
    toggleListening,
    
    // Utilities
    isReady: state.isConnected && !state.error,
    canSpeak: state.isConnected && !state.isSpeaking
  }
}