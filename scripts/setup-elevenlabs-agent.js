// scripts/setup-elevenlabs-agent.js
// Run this script to create the Eleven Labs voice agent for Macedon Ranges

require('dotenv').config({ path: '.env.local' })

const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY

if (!ELEVENLABS_API_KEY) {
  console.error('❌ Missing ELEVENLABS_API_KEY in .env.local')
  process.exit(1)
}

const agentConfig = {
  name: "Macedon Fencing Quote Assistant",
  first_message: "G'day! I'm here to help you with your fencing quote. What type of fencing project are you looking for today?",
  system_prompt: `You are a professional fencing consultant for Macedon Ranges Country Gates & Timber Fencing in Victoria, Australia. 
Your role is to help customers create accurate quotes for fencing projects.

Your knowledge includes:
- Different types of fencing: paling ($185/m), picket ($220/m), colorbond ($280/m)
- Gate options: single gates (base $1200 + $450/m width), double gates (base $2200 + $400/m width), sliding gates (base $3500 + $600/m width)
- Automation systems: single solar ($3002), double solar ($3159), electric ($2800)
- Standard fence height is 1.8m unless specified
- Labor rate is $85/hour plus $150 call-out fee
- Free travel within 20km of Riddells Creek

When gathering quote information:
1. Ask about the type of fencing or gates needed
2. Get accurate measurements (length for fences, width for gates)
3. Confirm material preferences
4. Check if automation is required for gates
5. Ask about the property location to calculate travel costs
6. Suggest appropriate posts and hardware

Important guidelines:
- Always be professional and friendly
- Use Australian terminology and measurements (meters, not feet)
- Provide rough estimates but mention final quotes need site inspection
- If unsure about specifics, ask clarifying questions
- Convert any verbal descriptions into specific measurements and product types
- Round up measurements to the nearest 0.5m for safety margin

For voice commands, extract:
- Product type (gate, fence, automation, post)
- Measurements (width, length, height)
- Material (paling, picket, colorbond, single, double)
- Quantity
- Any special requirements`,
  voice_id: "cgSgspJ2msm6clMCkdW9", // Professional voice
  language: "en",
  llm: "gpt-4-turbo",
  temperature: 0.7,
  max_tokens: 150,
  stability: 0.7,
  similarity_boost: 0.8,
  use_speaker_boost: true,
  optimize_streaming_latency: 3,
  turn_timeout: 7,
  max_duration_seconds: 600,
  record_voice: true,
  retention_days: 730
}

async function createAgent() {
  console.log('🚀 Creating Eleven Labs Voice Agent...')
  
  try {
    const response = await fetch('https://api.elevenlabs.io/v1/agents', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'xi-api-key': ELEVENLABS_API_KEY
      },
      body: JSON.stringify(agentConfig)
    })

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`)
    }

    const agent = await response.json()
    
    console.log('✅ Agent created successfully!')
    console.log('Agent ID:', agent.agent_id)
    console.log('\n📝 Next steps:')
    console.log('1. Update NEXT_PUBLIC_ELEVENLABS_AGENT_ID in .env.local with:', agent.agent_id)
    console.log('2. Configure webhooks in Eleven Labs dashboard for real-time events')
    console.log('3. Test the agent using the Eleven Labs playground')
    console.log('\n🎯 Agent Details:')
    console.log(JSON.stringify(agent, null, 2))
    
    // Save agent details to file
    const fs = require('fs')
    fs.writeFileSync(
      'scripts/agent-details.json',
      JSON.stringify({ ...agent, created_at: new Date().toISOString() }, null, 2)
    )
    console.log('\n💾 Agent details saved to scripts/agent-details.json')
    
  } catch (error) {
    console.error('❌ Failed to create agent:', error)
    process.exit(1)
  }
}

// Add knowledge base if needed
async function addKnowledgeBase(agentId) {
  console.log('\n📚 Adding knowledge base to agent...')
  
  const knowledgeBase = {
    name: "Macedon Ranges Fencing Products & Pricing",
    text: `
PRODUCT CATALOG - Macedon Ranges Country Gates & Timber Fencing

GATES:
- Single Gate: Base price $1,200 + $450 per meter of width
- Double Gate: Base price $2,200 + $400 per meter of width  
- Sliding Gate: Base price $3,500 + $600 per meter of width
- Standard heights: 1.5m, 1.8m, 2.1m

FENCING:
- Paling Fence: $185 per meter
- Picket Fence: $220 per meter
- Colorbond Fence: $280 per meter
- Standard height: 1.8m (other heights available)

AUTOMATION:
- Single Gate Solar Automation: $3,002 (includes remote controls)
- Double Gate Solar Automation: $3,159 (includes remote controls)
- Electric Gate Automation: $2,800 (requires power connection)

POSTS & HARDWARE:
- Standard Post: $85 each
- Corner Post: $120 each
- Gate Post: $150 each

LABOR & INSTALLATION:
- Labor Rate: $85 per hour
- Call-out Fee: $150 (waived for jobs over $2,000)
- Travel: Free within 20km of Riddells Creek
- Beyond 20km: $2 per km

SERVICE AREAS:
- Riddells Creek (base)
- Woodend
- Gisborne
- Mount Macedon
- Romsey
- Lancefield
- Macedon
- New Gisborne

TYPICAL PROJECT EXAMPLES:
1. Double Gate with Solar Automation:
   - 4m wide double gate: $2,200 + (4 × $400) = $3,800
   - Solar automation: $3,159
   - Installation (8 hours): $680
   - Total: $7,639 + GST

2. Colorbond Fence 30m:
   - Fencing: 30m × $280 = $8,400
   - Posts (11 posts): $935
   - Installation (16 hours): $1,360
   - Total: $10,695 + GST

WARRANTY:
- Workmanship: 7 years
- Gate automation: 2 years
- Timber products: 15 years (treated pine)
- Colorbond: 10 years manufacturer warranty
    `
  }
  
  try {
    const response = await fetch(`https://api.elevenlabs.io/v1/agents/${agentId}/knowledge-base`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'xi-api-key': ELEVENLABS_API_KEY
      },
      body: JSON.stringify(knowledgeBase)
    })

    if (!response.ok) {
      throw new Error(`Knowledge Base Error: ${response.status} ${response.statusText}`)
    }

    console.log('✅ Knowledge base added successfully!')
    
  } catch (error) {
    console.error('⚠️  Failed to add knowledge base:', error)
    // Non-critical error, continue
  }
}

// Main execution
async function main() {
  console.log('🏗️  Macedon Ranges Voice Agent Setup')
  console.log('=====================================\n')
  
  await createAgent()
  
  // If you want to add knowledge base, uncomment below:
  // const agentDetails = require('./agent-details.json')
  // await addKnowledgeBase(agentDetails.agent_id)
}

main().catch(console.error)