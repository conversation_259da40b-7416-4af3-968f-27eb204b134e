'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  FileText, 
  Search, 
  Download, 
  Upload, 
  Plus, 
  Eye, 
  Copy, 
  Trash2,
  BarChart3,
  TrendingUp,
  DollarSign
} from 'lucide-react'
import { quoteStorage } from '@/lib/storage/quoteStorage'
import { Quote } from '@/types/quote'
import Link from 'next/link'

export default function QuotesPage() {
  const [quotes, setQuotes] = useState<Quote[]>([])
  const [filteredQuotes, setFilteredQuotes] = useState<Quote[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    total: 0,
    byStatus: {} as Record<string, number>,
    totalValue: 0,
    averageValue: 0,
    thisMonth: 0,
    lastMonth: 0
  })

  // Load quotes on mount
  useEffect(() => {
    loadQuotes()
    loadStats()
  }, [])

  // Filter quotes when search or status changes
  useEffect(() => {
    filterQuotes()
  }, [quotes, searchTerm, selectedStatus])

  const loadQuotes = async () => {
    try {
      const allQuotes = await quoteStorage.getAllQuotes()
      setQuotes(allQuotes.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      ))
    } catch (error) {
      console.error('Failed to load quotes:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const statistics = await quoteStorage.getStatistics()
      setStats(statistics)
    } catch (error) {
      console.error('Failed to load statistics:', error)
    }
  }

  const filterQuotes = () => {
    let filtered = quotes

    // Filter by status
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(q => q.status === selectedStatus)
    }

    // Filter by search term
    if (searchTerm) {
      const search = searchTerm.toLowerCase()
      filtered = filtered.filter(q => 
        q.customerName.toLowerCase().includes(search) ||
        q.customerEmail.toLowerCase().includes(search) ||
        q.id.toLowerCase().includes(search) ||
        q.projectAddress.toLowerCase().includes(search)
      )
    }

    setFilteredQuotes(filtered)
  }

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this quote?')) {
      try {
        await quoteStorage.deleteQuote(id)
        await loadQuotes()
        await loadStats()
      } catch (error) {
        console.error('Failed to delete quote:', error)
      }
    }
  }

  const handleDuplicate = async (id: string) => {
    try {
      await quoteStorage.duplicateQuote(id)
      await loadQuotes()
    } catch (error) {
      console.error('Failed to duplicate quote:', error)
    }
  }

  const handleExport = async () => {
    try {
      const json = await quoteStorage.exportQuotes()
      const blob = new Blob([json], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `quotes-export-${new Date().toISOString().split('T')[0]}.json`
      a.click()
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to export quotes:', error)
    }
  }

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    try {
      const text = await file.text()
      const count = await quoteStorage.importQuotes(text)
      alert(`Successfully imported ${count} quotes`)
      await loadQuotes()
      await loadStats()
    } catch (error) {
      console.error('Failed to import quotes:', error)
      alert('Failed to import quotes. Please check the file format.')
    }
  }

  const getStatusColor = (status: Quote['status']) => {
    switch (status) {
      case 'draft': return 'secondary'
      case 'sent': return 'default'
      case 'accepted': return 'success'
      case 'revised': return 'warning'
      case 'expired': return 'destructive'
      case 'rejected': return 'destructive'
      default: return 'secondary'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount)
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-2">Quotes Management</h1>
        <p className="text-muted-foreground">
          View and manage all your fencing quotes
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Quotes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground mt-1">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              {stats.thisMonth} this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(stats.totalValue)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              All quotes combined
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Average Quote</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(stats.averageValue)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Per quote value
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Acceptance Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.total > 0 
                ? Math.round((stats.byStatus.accepted || 0) / stats.total * 100)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {stats.byStatus.accepted || 0} accepted
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Actions Bar */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by customer, email, or quote number..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <Link href="/">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Quote
            </Button>
          </Link>
          <Button variant="outline" onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <label>
            <Button variant="outline" asChild>
              <span>
                <Upload className="mr-2 h-4 w-4" />
                Import
                <input
                  type="file"
                  accept=".json"
                  className="hidden"
                  onChange={handleImport}
                />
              </span>
            </Button>
          </label>
        </div>
      </div>

      {/* Quotes Tabs */}
      <Tabs value={selectedStatus} onValueChange={setSelectedStatus}>
        <TabsList className="mb-4">
          <TabsTrigger value="all">
            All ({quotes.length})
          </TabsTrigger>
          <TabsTrigger value="draft">
            Draft ({stats.byStatus.draft || 0})
          </TabsTrigger>
          <TabsTrigger value="sent">
            Sent ({stats.byStatus.sent || 0})
          </TabsTrigger>
          <TabsTrigger value="accepted">
            Accepted ({stats.byStatus.accepted || 0})
          </TabsTrigger>
          <TabsTrigger value="revised">
            Revised ({stats.byStatus.revised || 0})
          </TabsTrigger>
        </TabsList>

        <TabsContent value={selectedStatus}>
          {loading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Loading quotes...</p>
            </div>
          ) : filteredQuotes.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  {searchTerm 
                    ? 'No quotes found matching your search'
                    : 'No quotes found'}
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredQuotes.map((quote) => (
                <Card key={quote.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-lg">
                            {quote.customerName || 'Unnamed Customer'}
                          </h3>
                          <Badge variant={getStatusColor(quote.status)}>
                            {quote.status}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm text-muted-foreground">
                          <div>Quote #: {quote.id}</div>
                          <div>Created: {new Date(quote.createdAt).toLocaleDateString()}</div>
                          <div>{quote.customerEmail}</div>
                          <div>{quote.customerPhone}</div>
                          <div className="sm:col-span-2">{quote.projectAddress}</div>
                        </div>
                        
                        <div className="mt-3 flex items-center gap-4">
                          <div className="text-sm">
                            <span className="text-muted-foreground">Items:</span>
                            <span className="ml-1 font-medium">{quote.items.length}</span>
                          </div>
                          <div className="text-sm">
                            <span className="text-muted-foreground">Total:</span>
                            <span className="ml-1 font-medium">
                              {formatCurrency(quote.total)}
                            </span>
                          </div>
                          <div className="text-sm">
                            <span className="text-muted-foreground">Valid until:</span>
                            <span className="ml-1 font-medium">
                              {new Date(quote.validUntil).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-2 ml-4">
                        <Link href={`/quotes/${quote.id}`}>
                          <Button size="sm" variant="ghost">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button 
                          size="sm" 
                          variant="ghost"
                          onClick={() => handleDuplicate(quote.id)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button 
                          size="sm" 
                          variant="ghost"
                          onClick={() => handleDelete(quote.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}