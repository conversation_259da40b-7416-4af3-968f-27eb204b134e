// app/layout.tsx
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { CopilotKit } from '@copilotkit/react-core'
import { CopilotPopup } from '@copilotkit/react-ui'
import '@copilotkit/react-ui/styles.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Macedon Ranges Voice Quoting Agent',
  description: 'AI-powered voice agent for generating fencing quotes',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <CopilotKit runtimeUrl="/api/copilotkit">
          {children}
          <CopilotPopup 
            instructions="You are a helpful assistant for Macedon Ranges Country Gates & Timber Fencing. Help users create accurate quotes for fencing projects, including gates, automation, and installation. Be professional and knowledgeable about Australian fencing standards and pricing."
            defaultOpen={true}
            labels={{
              title: "Fencing Quote Assistant",
              initial: "Hi! I'm here to help you create a quote for your fencing project. What type of fencing are you looking for?"
            }}
          />
        </CopilotKit>
      </body>
    </html>
  )
}