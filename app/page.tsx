'use client'

import { useState, useEffect } from 'react'
// import { useCopilotAction, useCopilotReadable } from '@copilotkit/react-core'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Save, Send, Calculator, FileText } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ElevenLabsVoiceAgent } from '@/components/voice/ElevenLabsVoiceAgent'

// Shared quote state interface (similar to recipe in the dojo demo)
interface FenceItem {
  id: string
  type: 'gate' | 'fence' | 'automation' | 'post'
  description: string
  dimensions: {
    width?: number
    height?: number
    length?: number
  }
  material: string
  quantity: number
  unitPrice: number
  totalPrice: number
}

interface Quote {
  id: string
  customerName: string
  customerEmail: string
  customerPhone: string
  projectAddress: string
  items: FenceItem[]
  laborCost: number
  travelCost: number
  subtotal: number
  gst: number
  total: number
  notes: string
  validUntil: string
  status: 'draft' | 'sent' | 'accepted' | 'revised'
  createdAt: string
  updatedAt: string
}

// Product catalog data (shared state)
const PRODUCT_CATALOG = {
  gates: {
    single: { basePrice: 1200, perMeter: 450 },
    double: { basePrice: 2200, perMeter: 400 },
    sliding: { basePrice: 3500, perMeter: 600 }
  },
  fencing: {
    paling: { perMeter: 185 },
    picket: { perMeter: 220 },
    colorbond: { perMeter: 280 }
  },
  automation: {
    singleSolar: { price: 3002 },
    doubleSolar: { price: 3159 },
    electric: { price: 2800 }
  },
  posts: {
    standard: { price: 85 },
    corner: { price: 120 },
    gate: { price: 150 }
  },
  labor: {
    hourlyRate: 85,
    callOutFee: 150
  }
}

export default function VoiceQuotingAgent() {
  // Initialize quote with default values
  const [currentQuote, setCurrentQuote] = useState<Quote>({
    id: `QU${Date.now()}`,
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    projectAddress: '',
    items: [],
    laborCost: 0,
    travelCost: 0,
    subtotal: 0,
    gst: 0,
    total: 0,
    notes: '',
    validUntil: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    status: 'draft',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  })

  // Note: Using regular React state instead of useCopilotState for compatibility

  // Make quote readable by Copilot (temporarily disabled)
  // useCopilotReadable({
  //   description: "Current quote being created with all items and calculations",
  //   value: currentQuote
  // })

  // Make product catalog readable (temporarily disabled)
  // useCopilotReadable({
  //   description: "Product pricing catalog for all fencing products and services",
  //   value: PRODUCT_CATALOG
  // })

  // Helper function to calculate item price
  function calculateItemPrice(type: string, material: string, dimensions: any): number {
    switch (type) {
      case 'gate':
        if (material.includes('single')) {
          return PRODUCT_CATALOG.gates.single.basePrice + (dimensions.width || 0) * PRODUCT_CATALOG.gates.single.perMeter
        } else if (material.includes('double')) {
          return PRODUCT_CATALOG.gates.double.basePrice + (dimensions.width || 0) * PRODUCT_CATALOG.gates.double.perMeter
        } else if (material.includes('sliding')) {
          return PRODUCT_CATALOG.gates.sliding.basePrice + (dimensions.width || 0) * PRODUCT_CATALOG.gates.sliding.perMeter
        }
        return 1500

      case 'fence':
        const fenceType = material.toLowerCase()
        if (fenceType.includes('paling')) {
          return (dimensions.length || 0) * PRODUCT_CATALOG.fencing.paling.perMeter
        } else if (fenceType.includes('picket')) {
          return (dimensions.length || 0) * PRODUCT_CATALOG.fencing.picket.perMeter
        } else if (fenceType.includes('colorbond')) {
          return (dimensions.length || 0) * PRODUCT_CATALOG.fencing.colorbond.perMeter
        }
        return (dimensions.length || 0) * 200

      case 'automation':
        if (material.includes('single') && material.includes('solar')) {
          return PRODUCT_CATALOG.automation.singleSolar.price
        } else if (material.includes('double') && material.includes('solar')) {
          return PRODUCT_CATALOG.automation.doubleSolar.price
        }
        return PRODUCT_CATALOG.automation.electric.price

      case 'post':
        if (material.includes('corner')) {
          return PRODUCT_CATALOG.posts.corner.price
        } else if (material.includes('gate')) {
          return PRODUCT_CATALOG.posts.gate.price
        }
        return PRODUCT_CATALOG.posts.standard.price

      default:
        return 0
    }
  }

  // Recalculate totals
  function recalculateTotals() {
    const itemsTotal = currentQuote.items.reduce((sum, item) => sum + item.totalPrice, 0)
    const subtotal = itemsTotal + currentQuote.laborCost + currentQuote.travelCost
    const gst = subtotal * 0.1
    const total = subtotal + gst

    const updatedQuote = {
      ...currentQuote,
      subtotal,
      gst,
      total,
      updatedAt: new Date().toISOString()
    }

    setCurrentQuote(updatedQuote)
  }

  // CopilotKit Actions for collaborative editing (temporarily disabled)
  /*
  useCopilotAction({
    name: "addQuoteItem",
    description: "Add a new item to the quote (gate, fence, automation, or post)",
    parameters: [
      { name: "type", type: "string", description: "Type of item (gate, fence, automation, post)" },
      { name: "description", type: "string", description: "Item description" },
      { name: "width", type: "number", description: "Width in meters (for gates)" },
      { name: "height", type: "number", description: "Height in meters" },
      { name: "length", type: "number", description: "Length in meters (for fencing)" },
      { name: "material", type: "string", description: "Material type (e.g., paling, picket, colorbond, single, double)" },
      { name: "quantity", type: "number", description: "Quantity of items" }
    ],
    handler: ({ type, description, width, height, length, material, quantity }) => {
      const newItem: FenceItem = {
        id: `ITEM-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        type: type as any,
        description: description || `${material} ${type}`,
        dimensions: { width, height, length },
        material: material || 'standard',
        quantity: quantity || 1,
        unitPrice: calculateItemPrice(type, material || '', { width, height, length }),
        totalPrice: 0
      }
      newItem.totalPrice = newItem.unitPrice * newItem.quantity

      const updatedQuote = {
        ...currentQuote,
        items: [...currentQuote.items, newItem],
        updatedAt: new Date().toISOString()
      }

      setCurrentQuote(updatedQuote)

      setTimeout(recalculateTotals, 100)
    }
  })
  */

  /*
  useCopilotAction({
    name: "updateCustomerInfo",
    description: "Update customer information for the quote",
    parameters: [
      { name: "name", type: "string", description: "Customer name" },
      { name: "email", type: "string", description: "Customer email" },
      { name: "phone", type: "string", description: "Customer phone" },
      { name: "address", type: "string", description: "Project address" }
    ],
    handler: ({ name, email, phone, address }) => {
      const updatedQuote = {
        ...currentQuote,
        customerName: name || currentQuote.customerName,
        customerEmail: email || currentQuote.customerEmail,
        customerPhone: phone || currentQuote.customerPhone,
        projectAddress: address || currentQuote.projectAddress,
        updatedAt: new Date().toISOString()
      }

      setCurrentQuote(updatedQuote)
    }
  })

  useCopilotAction({
    name: "updateLaborCost",
    description: "Update labor costs for the quote",
    parameters: [
      { name: "hours", type: "number", description: "Number of labor hours" },
      { name: "includeCallOut", type: "boolean", description: "Include call out fee" }
    ],
    handler: ({ hours, includeCallOut }) => {
      const laborCost = (hours || 0) * PRODUCT_CATALOG.labor.hourlyRate +
                       (includeCallOut ? PRODUCT_CATALOG.labor.callOutFee : 0)

      const updatedQuote = {
        ...currentQuote,
        laborCost,
        updatedAt: new Date().toISOString()
      }

      setCurrentQuote(updatedQuote)

      setTimeout(recalculateTotals, 100)
    }
  })

  useCopilotAction({
    name: "removeQuoteItem",
    description: "Remove an item from the quote",
    parameters: [
      { name: "itemId", type: "string", description: "ID of the item to remove" }
    ],
    handler: ({ itemId }) => {
      const updatedQuote = {
        ...currentQuote,
        items: currentQuote.items.filter(item => item.id !== itemId),
        updatedAt: new Date().toISOString()
      }

      setCurrentQuote(updatedQuote)

      setTimeout(recalculateTotals, 100)
    }
  })
  */

  // Handle voice commands from Eleven Labs
  const handleVoiceCommand = (command: any) => {
    if (command.action === 'addQuoteItem') {
      const { type, description, material, width, length, quantity } = command.params

      const newItem: FenceItem = {
        id: `ITEM-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        type,
        description: description || `${material} ${type}`,
        dimensions: { width, length, height: type === 'fence' ? 1.8 : undefined },
        material,
        quantity: quantity || 1,
        unitPrice: calculateItemPrice(type, material, { width, length }),
        totalPrice: 0
      }
      newItem.totalPrice = newItem.unitPrice * newItem.quantity

      const updatedQuote = {
        ...currentQuote,
        items: [...currentQuote.items, newItem],
        updatedAt: new Date().toISOString()
      }

      setCurrentQuote(updatedQuote)

      setTimeout(recalculateTotals, 100)
    }
  }

  // Save quote to local storage
  const saveQuote = () => {
    localStorage.setItem(`quote-${currentQuote.id}`, JSON.stringify(currentQuote))
    alert('Quote saved successfully!')
  }

  // Send quote (placeholder)
  const sendQuote = () => {
    console.log('Sending quote:', currentQuote)
    alert('Quote sending functionality would be implemented here')
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-2">Macedon Ranges Fencing Quote Builder</h1>
        <p className="text-muted-foreground">
          AI-powered voice assistant with collaborative editing
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        {/* Voice Agent */}
        <div className="lg:col-span-1">
          <ElevenLabsVoiceAgent
            agentId="macedon-fencing-agent"
            onCommand={handleVoiceCommand}
          />
        </div>

        {/* Quote Summary Card */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Quote Summary</CardTitle>
              <CardDescription>
                Quote #{currentQuote.id} • Valid until {new Date(currentQuote.validUntil).toLocaleDateString()}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Customer</p>
                  <p className="font-medium">{currentQuote.customerName || 'Not specified'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Location</p>
                  <p className="font-medium">{currentQuote.projectAddress || 'Not specified'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Items</p>
                  <p className="font-medium">{currentQuote.items.length} items</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total</p>
                  <p className="font-medium text-lg">${currentQuote.total.toFixed(2)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Main Quote Builder Tabs */}
      <Tabs defaultValue="customer" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="customer">Customer Info</TabsTrigger>
          <TabsTrigger value="items">Quote Items</TabsTrigger>
          <TabsTrigger value="costs">Costs & Labor</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="customer" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="customerName">Name</Label>
                  <Input
                    id="customerName"
                    value={currentQuote.customerName}
                    onChange={(e) => {
                      const updated = { ...currentQuote, customerName: e.target.value }
                      setCurrentQuote(updated)
                    }}
                    placeholder="John Smith"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="customerPhone">Phone</Label>
                  <Input
                    id="customerPhone"
                    value={currentQuote.customerPhone}
                    onChange={(e) => {
                      const updated = { ...currentQuote, customerPhone: e.target.value }
                      setCurrentQuote(updated)
                    }}
                    placeholder="0400 000 000"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="customerEmail">Email</Label>
                <Input
                  id="customerEmail"
                  type="email"
                  value={currentQuote.customerEmail}
                  onChange={(e) => {
                    const updated = { ...currentQuote, customerEmail: e.target.value }
                    setCurrentQuote(updated)
                  }}
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="projectAddress">Project Address</Label>
                <Input
                  id="projectAddress"
                  value={currentQuote.projectAddress}
                  onChange={(e) => {
                    const updated = { ...currentQuote, projectAddress: e.target.value }
                    setCurrentQuote(updated)
                  }}
                  placeholder="123 Main St, Woodend VIC 3442"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="items" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Quote Items</CardTitle>
              <CardDescription>
                Add items using voice commands or ask the AI assistant
              </CardDescription>
            </CardHeader>
            <CardContent>
              {currentQuote.items.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>No items added yet. Use voice commands or ask the assistant to add items.</p>
                  <p className="text-sm mt-2">Try saying "Add a double gate 4 meters wide"</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {currentQuote.items.map((item) => (
                    <Card key={item.id}>
                      <CardContent className="pt-6">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h4 className="font-semibold">{item.description}</h4>
                            <p className="text-sm text-muted-foreground">
                              {item.material} • Qty: {item.quantity}
                            </p>
                            {item.dimensions && (
                              <p className="text-sm text-muted-foreground">
                                Dimensions: {item.dimensions.width && `W: ${item.dimensions.width}m`}
                                {item.dimensions.height && ` H: ${item.dimensions.height}m`}
                                {item.dimensions.length && ` L: ${item.dimensions.length}m`}
                              </p>
                            )}
                          </div>
                          <div className="text-right">
                            <p className="font-semibold">${item.totalPrice.toFixed(2)}</p>
                            <p className="text-sm text-muted-foreground">
                              ${item.unitPrice.toFixed(2)} each
                            </p>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="mt-2"
                              onClick={() => {
                                const updated = {
                                  ...currentQuote,
                                  items: currentQuote.items.filter(i => i.id !== item.id)
                                }
                                setCurrentQuote(updated)
                                setTimeout(recalculateTotals, 100)
                              }}
                            >
                              Remove
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="costs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Labor & Additional Costs</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="laborHours">Labor Hours</Label>
                  <Input
                    id="laborHours"
                    type="number"
                    placeholder="0"
                    onChange={(e) => {
                      const hours = parseFloat(e.target.value) || 0
                      const laborCost = hours * PRODUCT_CATALOG.labor.hourlyRate
                      const updated = { ...currentQuote, laborCost }
                      setCurrentQuote(updated)
                      setTimeout(recalculateTotals, 100)
                    }}
                  />
                  <p className="text-sm text-muted-foreground">
                    ${PRODUCT_CATALOG.labor.hourlyRate}/hour
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="travelCost">Travel Cost</Label>
                  <Input
                    id="travelCost"
                    type="number"
                    value={currentQuote.travelCost}
                    onChange={(e) => {
                      const travelCost = parseFloat(e.target.value) || 0
                      const updated = { ...currentQuote, travelCost }
                      setCurrentQuote(updated)
                      setTimeout(recalculateTotals, 100)
                    }}
                    placeholder="0"
                  />
                  <p className="text-sm text-muted-foreground">
                    Within 20km: Free
                  </p>
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Items Total</span>
                  <span>${currentQuote.items.reduce((sum, item) => sum + item.totalPrice, 0).toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Labor Cost</span>
                  <span>${currentQuote.laborCost.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Travel Cost</span>
                  <span>${currentQuote.travelCost.toFixed(2)}</span>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>${currentQuote.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>GST (10%)</span>
                  <span>${currentQuote.gst.toFixed(2)}</span>
                </div>
                <Separator />
                <div className="flex justify-between text-xl font-bold">
                  <span>Total</span>
                  <span>${currentQuote.total.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preview" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>Quote Preview</CardTitle>
                  <CardDescription>
                    Review the quote before sending
                  </CardDescription>
                </div>
                <Badge>{currentQuote.status}</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Company Header */}
                <div className="text-center border-b pb-4">
                  <h2 className="text-2xl font-bold">Macedon Ranges Country Gates & Timber Fencing</h2>
                  <p className="text-muted-foreground">Professional Fencing Solutions</p>
                  <p className="text-sm">ABN: 12 ***********</p>
                </div>

                {/* Quote Details */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-semibold">Quote Number:</p>
                    <p>{currentQuote.id}</p>
                  </div>
                  <div>
                    <p className="font-semibold">Valid Until:</p>
                    <p>{new Date(currentQuote.validUntil).toLocaleDateString()}</p>
                  </div>
                </div>

                {/* Customer Details */}
                <div>
                  <p className="font-semibold mb-2">Customer Details:</p>
                  <div className="text-sm space-y-1">
                    <p>{currentQuote.customerName}</p>
                    <p>{currentQuote.customerEmail}</p>
                    <p>{currentQuote.customerPhone}</p>
                    <p>{currentQuote.projectAddress}</p>
                  </div>
                </div>

                {/* Quote Items */}
                <div>
                  <p className="font-semibold mb-2">Quote Items:</p>
                  <div className="space-y-2">
                    {currentQuote.items.map((item, index) => (
                      <div key={item.id} className="flex justify-between text-sm">
                        <span>{index + 1}. {item.description} (Qty: {item.quantity})</span>
                        <span>${item.totalPrice.toFixed(2)}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Totals */}
                <div className="border-t pt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Labor</span>
                    <span>${currentQuote.laborCost.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Travel</span>
                    <span>${currentQuote.travelCost.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Subtotal</span>
                    <span>${currentQuote.subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>GST</span>
                    <span>${currentQuote.gst.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between font-bold text-lg">
                    <span>Total (inc. GST)</span>
                    <span>${currentQuote.total.toFixed(2)}</span>
                  </div>
                </div>

                {/* Terms */}
                <div className="text-xs text-muted-foreground space-y-1 border-t pt-4">
                  <p>• This quote is valid for 14 days from the date of issue</p>
                  <p>• 50% deposit required before work commences</p>
                  <p>• Final measurements will be confirmed on-site</p>
                  <p>• All prices include GST</p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={saveQuote}>
                <Save className="mr-2 h-4 w-4" />
                Save Draft
              </Button>
              <Button onClick={sendQuote}>
                <Send className="mr-2 h-4 w-4" />
                Send Quote
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}