# Macedon Ranges Voice Agent - Deployment Guide

## Prerequisites

1. **Node.js 18+** and npm/yarn/bun
2. **Eleven Labs Account** with API access
3. **OpenAI Account** for CopilotKit
4. **Vercel Account** (recommended) or any Node.js hosting

## Local Development Setup

### 1. <PERSON><PERSON> and Install

```bash
cd /Users/<USER>/Projects/macedon-ranges-voice-agent
npm install
```

### 2. Environment Setup

```bash
npm run setup:env
```

Edit `.env.local` with your credentials:

```env
NEXT_PUBLIC_ELEVENLABS_API_KEY=your_api_key_here
OPENAI_API_KEY=your_openai_key_here
```

### 3. Create Eleven Labs Voice Agent

```bash
npm run setup:agent
```

This will:
- Create a voice agent configured for fencing quotes
- Save agent details to `scripts/agent-details.json`
- Display the agent ID to add to `.env.local`

### 4. Update Environment

Add the agent ID from step 3:

```env
NEXT_PUBLIC_ELEVENLABS_AGENT_ID=agent_id_from_setup
```

### 5. Run Development Server

```bash
npm run dev
```

Visit http://localhost:3000

## Production Deployment

### Vercel Deployment (Recommended)

1. **Push to GitHub**
```bash
git init
git add .
git commit -m "Initial commit"
git remote add origin https://github.com/your-username/macedon-voice-agent.git
git push -u origin main
```

2. **Deploy to Vercel**
- Go to https://vercel.com/new
- Import your GitHub repository
- Add environment variables:
  - `NEXT_PUBLIC_ELEVENLABS_API_KEY`
  - `NEXT_PUBLIC_ELEVENLABS_AGENT_ID`
  - `OPENAI_API_KEY`
- Deploy

### Alternative Deployments

#### Railway
```bash
npm install -g @railway/cli
railway login
railway init
railway add
railway deploy
```

#### Render
1. Create `render.yaml`:
```yaml
services:
  - type: web
    name: macedon-voice-agent
    env: node
    buildCommand: npm install && npm run build
    startCommand: npm start
    envVars:
      - key: NODE_VERSION
        value: 18
```

2. Connect GitHub and deploy

## Eleven Labs Configuration

### Voice Agent Settings

1. **Navigate to Eleven Labs Console**
   - https://elevenlabs.io/app/conversational-ai

2. **Configure Agent**
   - Voice: Professional Australian (cgSgspJ2msm6clMCkdW9)
   - Temperature: 0.7
   - Max response length: 150 tokens
   - Enable interruption handling

3. **Knowledge Base** (Optional)
   - Upload product catalog PDF
   - Add pricing information
   - Include standard terms

4. **Webhooks** (For production)
   ```
   https://your-domain.com/api/webhooks/elevenlabs
   ```

### API Rate Limits
- Free tier: 10,000 characters/month
- Starter: 100,000 characters/month
- Creator: 1,000,000 characters/month

## Security Best Practices

### 1. API Key Management
- Never commit `.env.local` to version control
- Use environment variables in production
- Rotate keys regularly

### 2. CORS Configuration
Add to `next.config.js`:
```javascript
module.exports = {
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: process.env.ALLOWED_ORIGIN || '*' },
        ],
      },
    ]
  },
}
```

### 3. Rate Limiting
Install rate limiting:
```bash
npm install express-rate-limit
```

### 4. Input Validation
- Sanitize all user inputs
- Validate quote data before storage
- Implement CSRF protection

## Monitoring & Analytics

### 1. Error Tracking (Sentry)
```bash
npm install @sentry/nextjs
```

Configure in `next.config.js`:
```javascript
const { withSentryConfig } = require('@sentry/nextjs')

module.exports = withSentryConfig(
  module.exports,
  { silent: true },
  { hideSourcemaps: true }
)
```

### 2. Analytics (Vercel Analytics)
```bash
npm install @vercel/analytics
```

Add to `app/layout.tsx`:
```typescript
import { Analytics } from '@vercel/analytics/react'

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <Analytics />
      </body>
    </html>
  )
}
```

### 3. Performance Monitoring
- Monitor API response times
- Track voice agent connection success rate
- Log quote generation metrics

## Backup & Recovery

### 1. Quote Data Backup
Implement automated backups:
```javascript
// scripts/backup-quotes.js
const backup = async () => {
  const quotes = await quoteStorage.exportQuotes()
  // Upload to S3, Google Drive, etc.
}
```

### 2. Database Migration
For production, consider migrating from IndexedDB to:
- PostgreSQL (via Supabase)
- MongoDB (via MongoDB Atlas)
- Firebase Firestore

## Troubleshooting

### Common Issues

1. **Voice Agent Not Connecting**
   - Check API key validity
   - Verify agent ID is correct
   - Check browser microphone permissions
   - Ensure HTTPS in production

2. **Quotes Not Saving**
   - Check IndexedDB storage limits
   - Verify browser compatibility
   - Clear browser cache

3. **CopilotKit Errors**
   - Verify OpenAI API key
   - Check API rate limits
   - Review CopilotKit logs

### Debug Mode
Enable debug logging:
```javascript
// In components/voice/ElevenLabsVoiceAgent.tsx
useElevenLabsAgent({
  debug: process.env.NODE_ENV === 'development'
})
```

## Support Resources

- **Eleven Labs Documentation**: https://docs.elevenlabs.io
- **CopilotKit Documentation**: https://docs.copilotkit.ai
- **Next.js Documentation**: https://nextjs.org/docs
- **Support Email**: <EMAIL>

## License

Proprietary - ALIAS Labs © 2024