// components/voice/ElevenLabsVoiceAgent.tsx
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Mic, MicOff, Phone, PhoneOff, Volume2, Loader2, AlertCircle } from 'lucide-react'
import { useElevenLabsAgent } from '@/hooks/useElevenLabsAgent'

interface ElevenLabsVoiceAgentProps {
  agentId?: string
  onTranscript?: (transcript: string) => void
  onCommand?: (command: any) => void
}

export function ElevenLabsVoiceAgent({ 
  agentId = process.env.NEXT_PUBLIC_ELEVENLABS_AGENT_ID || "default-fencing-agent",
  onTranscript,
  onCommand 
}: ElevenLabsVoiceAgentProps) {
  const [showDemo, setShowDemo] = useState(false)

  const {
    isConnected,
    isListening,
    isSpeaking,
    connectionStatus,
    transcript,
    agentResponse,
    error,
    connect,
    disconnect,
    toggleListening
  } = useElevenLabsAgent({
    agentId,
    onTranscript,
    onCommand,
    debug: true
  })

  // Demo mode for when API key is not available
  useEffect(() => {
    if (!process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY) {
      setShowDemo(true)
    }
  }, [])

  // Simulated demo commands
  const runDemoCommand = (command: string) => {
    if (onTranscript) {
      onTranscript(command)
    }
    
    // Parse the demo command
    const lowerCommand = command.toLowerCase()
    
    if (lowerCommand.includes('double gate') && lowerCommand.includes('4 meter')) {
      onCommand?.({
        action: 'addQuoteItem',
        params: {
          type: 'gate',
          description: 'Double Gate',
          material: 'double',
          width: 4,
          quantity: 1
        }
      })
    } else if (lowerCommand.includes('solar automation')) {
      onCommand?.({
        action: 'addQuoteItem',
        params: {
          type: 'automation',
          description: 'Double Gate Solar Automation',
          material: 'double solar',
          quantity: 1
        }
      })
    } else if (lowerCommand.includes('colorbond') && lowerCommand.includes('25 meter')) {
      onCommand?.({
        action: 'addQuoteItem',
        params: {
          type: 'fence',
          description: 'Colorbond Fence',
          material: 'colorbond',
          length: 25,
          height: 1.8,
          quantity: 1
        }
      })
    } else if (lowerCommand.includes('8 hours') && lowerCommand.includes('labor')) {
      onCommand?.({
        action: 'updateLaborCost',
        params: {
          hours: 8,
          includeCallOut: true
        }
      })
    }
  }

  const demoCommands = [
    "I need a double gate about 4 meters wide",
    "Add solar automation to that gate",
    "I also need 25 meters of colorbond fencing",
    "Include 8 hours of labor"
  ]

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Voice Assistant</CardTitle>
            <CardDescription>
              {showDemo 
                ? 'Demo Mode - Click commands below to simulate'
                : 'Powered by ElevenLabs Conversational AI'}
            </CardDescription>
          </div>
          <Badge 
            variant={
              connectionStatus === 'connected' ? 'default' : 
              connectionStatus === 'error' ? 'destructive' :
              'secondary'
            }
          >
            {connectionStatus}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Error Display */}
        {error && (
          <div className="bg-destructive/10 text-destructive rounded-lg p-4 flex items-start gap-2">
            <AlertCircle className="h-5 w-5 mt-0.5" />
            <div className="flex-1">
              <p className="font-medium">Connection Error</p>
              <p className="text-sm">{error.message}</p>
            </div>
          </div>
        )}

        {/* Connection Button */}
        {!showDemo ? (
          <div className="flex items-center justify-center">
            {!isConnected ? (
              <Button
                size="lg"
                onClick={connect}
                disabled={connectionStatus === 'connecting'}
                className="w-48"
              >
                {connectionStatus === 'connecting' ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  <>
                    <Phone className="mr-2 h-5 w-5" />
                    Start Voice Agent
                  </>
                )}
              </Button>
            ) : (
              <Button
                size="lg"
                variant="destructive"
                onClick={disconnect}
                className="w-48"
              >
                <PhoneOff className="mr-2 h-5 w-5" />
                End Call
              </Button>
            )}
          </div>
        ) : (
          <div className="text-center">
            <p className="text-sm text-muted-foreground mb-4">
              No API key detected. Running in demo mode.
            </p>
          </div>
        )}

        {/* Voice Activity Indicator */}
        {isConnected && (
          <div className="flex items-center justify-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleListening}
              className="flex items-center space-x-2"
            >
              {isListening ? (
                <>
                  <div className="relative">
                    <Mic className="h-6 w-6 text-green-500" />
                    <div className="absolute -inset-1 bg-green-500 rounded-full animate-ping opacity-25" />
                  </div>
                  <span className="text-sm text-green-600">Listening...</span>
                </>
              ) : (
                <>
                  <MicOff className="h-6 w-6 text-gray-400" />
                  <span className="text-sm text-gray-500">Muted</span>
                </>
              )}
            </Button>

            {isSpeaking && (
              <div className="flex items-center space-x-2">
                <Volume2 className="h-5 w-5 text-blue-500" />
                <div className="flex space-x-1">
                  {[1, 2, 3, 4, 5].map((bar) => (
                    <div
                      key={bar}
                      className={`w-1 bg-blue-500 animate-pulse`}
                      style={{
                        height: `${Math.random() * 20 + 10}px`,
                        animationDelay: `${bar * 0.1}s`
                      }}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Transcript Display */}
        {transcript && (
          <div className="bg-muted rounded-lg p-4">
            <p className="text-sm font-medium mb-1">You said:</p>
            <p className="text-sm italic">"{transcript}"</p>
          </div>
        )}

        {/* Agent Response Display */}
        {agentResponse && (
          <div className="bg-primary/10 rounded-lg p-4">
            <p className="text-sm font-medium mb-1">Assistant:</p>
            <p className="text-sm">"{agentResponse}"</p>
          </div>
        )}

        {/* Demo Commands */}
        {showDemo && (
          <div className="space-y-2">
            <p className="text-sm font-medium text-center mb-2">
              Click to simulate voice commands:
            </p>
            {demoCommands.map((command, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => runDemoCommand(command)}
                className="w-full text-left justify-start"
              >
                <Mic className="mr-2 h-4 w-4" />
                "{command}"
              </Button>
            ))}
          </div>
        )}

        {/* Instructions */}
        {!showDemo && (
          <div className="text-center text-sm text-muted-foreground space-y-1">
            <p>Say things like:</p>
            <p className="italic">"I need a double gate 4 meters wide with solar automation"</p>
            <p className="italic">"Add 30 meters of colorbond fencing"</p>
            <p className="italic">"Include labor costs for 8 hours"</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}