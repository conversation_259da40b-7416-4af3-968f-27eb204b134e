# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Install dependencies (prefer bun or pnpm over npm)
bun install

# Run development server
bun run dev

# Build for production
bun run build

# Start production server  
bun run start

# Run linting
bun run lint

# Setup environment variables
bun run setup:env

# Create Eleven Labs voice agent
bun run setup:agent
```

## Architecture Overview

This is a Next.js 14 application using the App Router pattern with three main AI integrations:

1. **Eleven Labs Conversational AI** - Voice input/output via WebSocket connection
2. **CopilotKit** - Collaborative AI assistant using GPT-4 for quote editing
3. **Shared State Pattern** - Quote state synchronized between voice agent, U<PERSON>, and AI assistant

### Key Architectural Decisions

- **State Management**: Uses `useCopilotState` for shared state between all interfaces. The quote object is the single source of truth.
- **Voice Processing**: WebSocket connection in `useElevenLabsAgent` hook handles real-time audio streaming and command parsing
- **Product Catalog**: Centralized in `lib/quoteGenerator.ts` with Australian pricing and calculations
- **API Routes**: Edge runtime for CopilotKit integration at `/api/copilotkit`

### Core Data Flow

1. Voice commands → WebSocket → `useElevenLabsAgent` → Parse commands → Update quote state
2. UI interactions → Direct state updates → Trigger recalculations
3. AI assistant → CopilotKit actions → Update quote state → UI reflects changes
4. All state changes → Automatic price recalculation → Real-time preview updates

## Important Patterns

### Voice Command Parsing

The voice agent uses regex patterns in `hooks/useElevenLabsAgent.ts` to parse natural language:
- Gate commands: `/(?:add|I need|need|want)?\s*(?:a|an)?\s*(single|double|sliding)\s*gate/i`
- Fencing commands: `/(\d+)\s*(?:meters?|m)\s*(?:of)?\s*(paling|picket|colorbond)/i`
- Automation: `/(?:add|include)?\s*(solar|electric)\s*(?:automation|opener)/i`

### Quote State Structure

```typescript
interface Quote {
  customerName: string
  customerEmail: string
  customerPhone: string
  propertyAddress: string
  items: QuoteItem[]
  laborHours: number
  includeCallOut: boolean
  notes: string
  validUntil: string
}
```

### Testing Voice Agent

For testing without API keys, the voice agent includes demo mode that simulates responses based on input patterns.

## Environment Configuration

Required environment variables:
```env
NEXT_PUBLIC_ELEVENLABS_API_KEY=your_key
NEXT_PUBLIC_ELEVENLABS_AGENT_ID=your_agent_id
OPENAI_API_KEY=your_openai_key
```

## Common Development Tasks

### Adding New Product Types

1. Update product catalog in `lib/quoteGenerator.ts`
2. Add voice command patterns in `hooks/useElevenLabsAgent.ts`
3. Update TypeScript types in `types/quote.ts`
4. Add UI components if needed

### Modifying Voice Commands

Voice command parsing is centralized in the `useElevenLabsAgent` hook. Add new regex patterns and corresponding handlers in the command processing section.

### Updating Pricing

All pricing is defined in the `PRODUCT_CATALOG` constant in `lib/quoteGenerator.ts`. GST (10%) is automatically calculated.

## Key Files to Understand

- `app/page.tsx` - Main quote builder with tabbed interface
- `hooks/useElevenLabsAgent.ts` - Voice agent WebSocket and command parsing
- `lib/quoteGenerator.ts` - Product catalog and price calculations
- `components/voice/ElevenLabsVoiceAgent.tsx` - Voice UI component