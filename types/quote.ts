// types/quote.ts
export interface FenceItem {
  id: string
  type: 'gate' | 'fence' | 'automation' | 'post'
  description: string
  dimensions: {
    width?: number
    height?: number
    length?: number
  }
  material: string
  quantity: number
  unitPrice: number
  totalPrice: number
}

export interface Quote {
  id: string
  customerName: string
  customerEmail: string
  customerPhone: string
  projectAddress: string
  items: FenceItem[]
  laborCost: number
  travelCost: number
  subtotal: number
  gst: number
  total: number
  notes: string
  validUntil: string
  status: 'draft' | 'sent' | 'accepted' | 'revised' | 'expired' | 'rejected'
  createdAt: string
  updatedAt: string
  sentAt?: string
  acceptedAt?: string
  completedAt?: string
}

export interface QuoteTemplate {
  id: string
  name: string
  description: string
  items: Omit<FenceItem, 'id'>[]
  createdAt: string
  updatedAt: string
}

export interface Customer {
  id: string
  name: string
  email: string
  phone: string
  addresses: string[]
  notes: string
  createdAt: string
  updatedAt: string
  totalQuotes: number
  totalValue: number
}