# Macedon Ranges Voice Quoting Agent

An AI-powered voice agent for generating fencing quotes using Eleven Labs Conversational AI and CopilotKit for collaborative editing.

## Features

- 🎙️ **Voice-Powered Quote Generation**: Natural language voice commands to add items to quotes
- 🤖 **AI Assistant Integration**: CopilotKit for intelligent quote assistance
- 🔄 **Shared State Management**: Real-time collaborative editing (like the dojo recipe demo)
- 📊 **Intelligent Pricing**: Automatic price calculation based on product catalog
- 📱 **Responsive Design**: Works on desktop and mobile devices
- 🎨 **Modern UI**: Built with Tailwind CSS and Shadcn UI components

## Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **AI Integration**: 
  - Eleven Labs Conversational AI (Voice Agent)
  - CopilotKit (Collaborative AI Assistant)
- **Styling**: Tailwind CSS, Shadcn UI
- **State Management**: React hooks with shared state pattern

## Project Structure

```
macedon-ranges-voice-agent/
├── app/
│   ├── api/
│   │   └── copilotkit/       # CopilotKit API endpoint
│   ├── layout.tsx            # Root layout with CopilotKit provider
│   ├── page.tsx              # Main quote builder page
│   └── globals.css           # Global styles
├── components/
│   ├── ui/                   # Shadcn UI components
│   └── voice/
│       └── ElevenLabsVoiceAgent.tsx  # Voice agent component
├── lib/
│   └── utils.ts              # Utility functions
└── package.json
```

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm/yarn/pnpm
- Eleven Labs API Key
- OpenAI API Key (for CopilotKit)

### Installation

1. Clone the repository:
```bash
cd /Users/<USER>/Projects/macedon-ranges-voice-agent
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env.local` file:
```env
NEXT_PUBLIC_ELEVENLABS_API_KEY=your_elevenlabs_api_key
OPENAI_API_KEY=your_openai_api_key
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000)

## Setting Up Eleven Labs Voice Agent

### 1. Create the Agent

Use the Eleven Labs API or dashboard to create a conversational agent with these settings:

```javascript
{
  name: "Macedon Fencing Assistant",
  first_message: "G'day! I'm here to help you with your fencing quote. What type of fencing project are you looking for today?",
  system_prompt: `You are a professional fencing consultant for Macedon Ranges Country Gates & Timber Fencing...`,
  voice_id: "cgSgspJ2msm6clMCkdW9", // Professional Australian voice
  language: "en",
  temperature: 0.7,
  stability: 0.7,
  similarity_boost: 0.8
}
```

### 2. Configure Voice Settings

The voice agent is configured to:
- Use Australian terminology and expressions
- Understand fencing-specific commands
- Extract measurements and specifications
- Handle natural conversation flow

## Voice Commands

The agent understands natural language commands like:

- "I need a double gate about 4 meters wide"
- "Add solar automation to that gate"
- "I need 30 meters of colorbond fencing"
- "Include 8 hours of labor"
- "What's the total cost?"

## Shared State Architecture

The application uses a shared state pattern (similar to the dojo recipe demo) for:

1. **Quote State**: Synchronized between voice agent, UI, and AI assistant
2. **Product Catalog**: Shared pricing information
3. **Real-time Updates**: Changes from any source update all components

## Key Components

### ElevenLabsVoiceAgent

Handles voice interactions and converts speech to structured commands:

```typescript
interface VoiceCommand {
  action: 'addQuoteItem' | 'updateCustomer' | 'calculateQuote'
  params: {
    type: string
    material: string
    dimensions: object
    quantity: number
  }
}
```

### CopilotKit Integration

Provides AI assistance for:
- Adding quote items
- Updating customer information
- Calculating costs
- Suggesting appropriate products

### Quote Builder

Main interface with tabs for:
- Customer Information
- Quote Items
- Labor & Costs
- Preview & Send

## Pricing Structure

```javascript
const PRODUCT_CATALOG = {
  gates: {
    single: { basePrice: 1200, perMeter: 450 },
    double: { basePrice: 2200, perMeter: 400 },
    sliding: { basePrice: 3500, perMeter: 600 }
  },
  fencing: {
    paling: { perMeter: 185 },
    picket: { perMeter: 220 },
    colorbond: { perMeter: 280 }
  },
  automation: {
    singleSolar: { price: 3002 },
    doubleSolar: { price: 3159 },
    electric: { price: 2800 }
  }
}
```

## Development

### Adding New Product Types

1. Update the `PRODUCT_CATALOG` in `page.tsx`
2. Add handling in `calculateItemPrice()`
3. Update voice command processing in `ElevenLabsVoiceAgent`
4. Add CopilotKit action if needed

### Customizing Voice Responses

Modify the `systemPrompt` in `ElevenLabsVoiceAgent.tsx` to change:
- Greeting style
- Information gathering flow
- Product recommendations
- Pricing guidance

## Production Deployment

1. Set up environment variables in your hosting platform
2. Configure Eleven Labs webhooks for real-time voice processing
3. Set up proper CORS headers for voice agent
4. Enable SSL for secure voice transmission

## License

Proprietary - ALIAS Labs

## Support

For support, contact the ALIAS Labs team.