// lib/storage/quoteStorage.ts
import { Quote } from '@/types/quote'

const DB_NAME = 'MacedonRangesQuotes'
const DB_VERSION = 1
const STORE_NAME = 'quotes'

class QuoteStorage {
  private db: IDBDatabase | null = null

  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION)

      request.onerror = () => {
        reject(new Error('Failed to open database'))
      }

      request.onsuccess = () => {
        this.db = request.result
        resolve()
      }

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result

        // Create quotes object store
        if (!db.objectStoreNames.contains(STORE_NAME)) {
          const store = db.createObjectStore(STORE_NAME, { keyPath: 'id' })
          
          // Create indexes for searching
          store.createIndex('customerName', 'customerName', { unique: false })
          store.createIndex('customerEmail', 'customerEmail', { unique: false })
          store.createIndex('status', 'status', { unique: false })
          store.createIndex('createdAt', 'createdAt', { unique: false })
          store.createIndex('updatedAt', 'updatedAt', { unique: false })
        }
      }
    })
  }

  async saveQuote(quote: Quote): Promise<void> {
    if (!this.db) await this.initialize()

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], 'readwrite')
      const store = transaction.objectStore(STORE_NAME)
      
      const request = store.put({
        ...quote,
        updatedAt: new Date().toISOString()
      })

      request.onsuccess = () => resolve()
      request.onerror = () => reject(new Error('Failed to save quote'))
    })
  }

  async getQuote(id: string): Promise<Quote | null> {
    if (!this.db) await this.initialize()

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], 'readonly')
      const store = transaction.objectStore(STORE_NAME)
      const request = store.get(id)

      request.onsuccess = () => {
        resolve(request.result || null)
      }
      request.onerror = () => reject(new Error('Failed to get quote'))
    })
  }

  async getAllQuotes(): Promise<Quote[]> {
    if (!this.db) await this.initialize()

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], 'readonly')
      const store = transaction.objectStore(STORE_NAME)
      const request = store.getAll()

      request.onsuccess = () => {
        resolve(request.result || [])
      }
      request.onerror = () => reject(new Error('Failed to get quotes'))
    })
  }

  async searchQuotes(params: {
    customerName?: string
    customerEmail?: string
    status?: string
    startDate?: string
    endDate?: string
  }): Promise<Quote[]> {
    if (!this.db) await this.initialize()

    const quotes = await this.getAllQuotes()

    return quotes.filter(quote => {
      if (params.customerName && !quote.customerName.toLowerCase().includes(params.customerName.toLowerCase())) {
        return false
      }
      if (params.customerEmail && !quote.customerEmail.toLowerCase().includes(params.customerEmail.toLowerCase())) {
        return false
      }
      if (params.status && quote.status !== params.status) {
        return false
      }
      if (params.startDate && new Date(quote.createdAt) < new Date(params.startDate)) {
        return false
      }
      if (params.endDate && new Date(quote.createdAt) > new Date(params.endDate)) {
        return false
      }
      return true
    })
  }

  async deleteQuote(id: string): Promise<void> {
    if (!this.db) await this.initialize()

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], 'readwrite')
      const store = transaction.objectStore(STORE_NAME)
      const request = store.delete(id)

      request.onsuccess = () => resolve()
      request.onerror = () => reject(new Error('Failed to delete quote'))
    })
  }

  async updateQuoteStatus(id: string, status: Quote['status']): Promise<void> {
    const quote = await this.getQuote(id)
    if (!quote) throw new Error('Quote not found')

    quote.status = status
    quote.updatedAt = new Date().toISOString()

    await this.saveQuote(quote)
  }

  async duplicateQuote(id: string): Promise<Quote> {
    const originalQuote = await this.getQuote(id)
    if (!originalQuote) throw new Error('Quote not found')

    const newQuote: Quote = {
      ...originalQuote,
      id: `QU${Date.now()}`,
      status: 'draft',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      validUntil: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    }

    await this.saveQuote(newQuote)
    return newQuote
  }

  // Export quotes to JSON
  async exportQuotes(quoteIds?: string[]): Promise<string> {
    let quotes: Quote[]
    
    if (quoteIds && quoteIds.length > 0) {
      quotes = []
      for (const id of quoteIds) {
        const quote = await this.getQuote(id)
        if (quote) quotes.push(quote)
      }
    } else {
      quotes = await this.getAllQuotes()
    }

    return JSON.stringify(quotes, null, 2)
  }

  // Import quotes from JSON
  async importQuotes(jsonData: string): Promise<number> {
    try {
      const quotes = JSON.parse(jsonData) as Quote[]
      let imported = 0

      for (const quote of quotes) {
        // Generate new ID to avoid conflicts
        const newQuote = {
          ...quote,
          id: `QU${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
        
        await this.saveQuote(newQuote)
        imported++
      }

      return imported
    } catch (error) {
      throw new Error('Invalid JSON data')
    }
  }

  // Get quote statistics
  async getStatistics(): Promise<{
    total: number
    byStatus: Record<string, number>
    totalValue: number
    averageValue: number
    thisMonth: number
    lastMonth: number
  }> {
    const quotes = await this.getAllQuotes()
    
    const now = new Date()
    const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)
    const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0)

    const stats = {
      total: quotes.length,
      byStatus: {} as Record<string, number>,
      totalValue: 0,
      averageValue: 0,
      thisMonth: 0,
      lastMonth: 0
    }

    quotes.forEach(quote => {
      // Status count
      stats.byStatus[quote.status] = (stats.byStatus[quote.status] || 0) + 1
      
      // Total value
      stats.totalValue += quote.total
      
      // Monthly counts
      const createdDate = new Date(quote.createdAt)
      if (createdDate >= thisMonthStart) {
        stats.thisMonth++
      } else if (createdDate >= lastMonthStart && createdDate <= lastMonthEnd) {
        stats.lastMonth++
      }
    })

    stats.averageValue = quotes.length > 0 ? stats.totalValue / quotes.length : 0

    return stats
  }
}

// Export singleton instance
export const quoteStorage = new QuoteStorage()