// lib/quoteGenerator.ts
import { Quote, FenceItem } from '@/types/quote'

export const PRODUCT_CATALOG = {
  gates: {
    single: { 
      basePrice: 1200, 
      perMeter: 450,
      description: 'Single Swing Gate',
      standardHeights: [1.5, 1.8, 2.1]
    },
    double: { 
      basePrice: 2200, 
      perMeter: 400,
      description: 'Double Swing Gate',
      standardHeights: [1.5, 1.8, 2.1]
    },
    sliding: { 
      basePrice: 3500, 
      perMeter: 600,
      description: 'Sliding Gate',
      standardHeights: [1.8, 2.1]
    }
  },
  fencing: {
    paling: { 
      perMeter: 185,
      description: 'Treated Pine Paling Fence',
      standardHeight: 1.8
    },
    picket: { 
      perMeter: 220,
      description: 'Picket Fence',
      standardHeight: 1.2
    },
    colorbond: { 
      perMeter: 280,
      description: 'Colorbond Steel Fence',
      standardHeights: [1.5, 1.8, 2.1]
    }
  },
  automation: {
    singleSolar: { 
      price: 3002,
      description: 'Single Gate Solar Automation Kit',
      includes: ['2 x Remote Controls', 'Solar Panel', 'Battery Backup']
    },
    doubleSolar: { 
      price: 3159,
      description: 'Double Gate Solar Automation Kit',
      includes: ['2 x Remote Controls', 'Solar Panel', 'Battery Backup']
    },
    electric: { 
      price: 2800,
      description: 'Electric Gate Automation Kit',
      includes: ['2 x Remote Controls', 'Control Box', 'Safety Sensors']
    }
  },
  posts: {
    standard: { 
      price: 85,
      description: 'Standard Fence Post (100x100mm)',
      height: 2.4
    },
    corner: { 
      price: 120,
      description: 'Corner/End Post (125x125mm)',
      height: 2.4
    },
    gate: { 
      price: 150,
      description: 'Gate Post (150x150mm)',
      height: 2.7
    }
  },
  labor: {
    hourlyRate: 85,
    callOutFee: 150,
    minHours: 4
  },
  travel: {
    freeRadius: 20, // km
    ratePerKm: 2
  }
}

export class QuoteGenerator {
  static generateQuoteNumber(): string {
    const now = new Date()
    const year = now.getFullYear().toString().slice(-2)
    const month = (now.getMonth() + 1).toString().padStart(2, '0')
    const random = Math.random().toString(36).substr(2, 4).toUpperCase()
    return `QU${year}${month}${random}`
  }

  static calculateItemPrice(type: string, material: string, dimensions: any): number {
    switch (type) {
      case 'gate':
        if (material.includes('single')) {
          return PRODUCT_CATALOG.gates.single.basePrice + 
                 (dimensions.width || 0) * PRODUCT_CATALOG.gates.single.perMeter
        } else if (material.includes('double')) {
          return PRODUCT_CATALOG.gates.double.basePrice + 
                 (dimensions.width || 0) * PRODUCT_CATALOG.gates.double.perMeter
        } else if (material.includes('sliding')) {
          return PRODUCT_CATALOG.gates.sliding.basePrice + 
                 (dimensions.width || 0) * PRODUCT_CATALOG.gates.sliding.perMeter
        }
        return 1500
      
      case 'fence':
        const fenceType = material.toLowerCase()
        if (fenceType.includes('paling')) {
          return (dimensions.length || 0) * PRODUCT_CATALOG.fencing.paling.perMeter
        } else if (fenceType.includes('picket')) {
          return (dimensions.length || 0) * PRODUCT_CATALOG.fencing.picket.perMeter
        } else if (fenceType.includes('colorbond')) {
          return (dimensions.length || 0) * PRODUCT_CATALOG.fencing.colorbond.perMeter
        }
        return (dimensions.length || 0) * 200
      
      case 'automation':
        if (material.includes('single') && material.includes('solar')) {
          return PRODUCT_CATALOG.automation.singleSolar.price
        } else if (material.includes('double') && material.includes('solar')) {
          return PRODUCT_CATALOG.automation.doubleSolar.price
        }
        return PRODUCT_CATALOG.automation.electric.price
      
      case 'post':
        if (material.includes('corner')) {
          return PRODUCT_CATALOG.posts.corner.price
        } else if (material.includes('gate')) {
          return PRODUCT_CATALOG.posts.gate.price
        }
        return PRODUCT_CATALOG.posts.standard.price
      
      default:
        return 0
    }
  }

  static calculatePostsNeeded(fenceLength: number): { standard: number, corner: number } {
    // Posts every 2.4m for standard fence
    const postSpacing = 2.4
    const totalPosts = Math.ceil(fenceLength / postSpacing) + 1
    
    return {
      standard: Math.max(0, totalPosts - 2), // Subtract corner posts
      corner: 2 // Always need 2 corner posts minimum
    }
  }

  static estimateLaborHours(quote: Quote): number {
    let hours = 0
    
    // Base time for call-out and setup
    hours += 2
    
    quote.items.forEach(item => {
      switch (item.type) {
        case 'gate':
          // Single gate: 4 hours, Double: 6 hours, Sliding: 8 hours
          if (item.material.includes('single')) hours += 4
          else if (item.material.includes('double')) hours += 6
          else if (item.material.includes('sliding')) hours += 8
          break
        
        case 'fence':
          // Approximately 10m per hour for standard fencing
          hours += (item.dimensions.length || 0) / 10
          break
        
        case 'automation':
          // 3-4 hours for automation installation
          hours += item.material.includes('solar') ? 4 : 3
          break
        
        case 'post':
          // 30 minutes per post
          hours += item.quantity * 0.5
          break
      }
    })
    
    // Round up to nearest hour
    return Math.ceil(hours)
  }

  static calculateTravelCost(distance: number): number {
    if (distance <= PRODUCT_CATALOG.travel.freeRadius) {
      return 0
    }
    
    const chargeableDistance = distance - PRODUCT_CATALOG.travel.freeRadius
    return chargeableDistance * PRODUCT_CATALOG.travel.ratePerKm * 2 // Return trip
  }

  static calculateQuoteTotals(quote: Quote): Quote {
    const itemsTotal = quote.items.reduce((sum, item) => sum + item.totalPrice, 0)
    const subtotal = itemsTotal + quote.laborCost + quote.travelCost
    const gst = subtotal * 0.1
    const total = subtotal + gst
    
    return {
      ...quote,
      subtotal,
      gst,
      total
    }
  }

  static createEmptyQuote(): Quote {
    return {
      id: this.generateQuoteNumber(),
      customerName: '',
      customerEmail: '',
      customerPhone: '',
      projectAddress: '',
      items: [],
      laborCost: 0,
      travelCost: 0,
      subtotal: 0,
      gst: 0,
      total: 0,
      notes: '',
      validUntil: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      status: 'draft',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  static validateQuote(quote: Quote): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!quote.customerName) errors.push('Customer name is required')
    if (!quote.customerEmail && !quote.customerPhone) {
      errors.push('Either email or phone number is required')
    }
    if (quote.customerEmail && !this.isValidEmail(quote.customerEmail)) {
      errors.push('Invalid email format')
    }
    if (!quote.projectAddress) errors.push('Project address is required')
    if (quote.items.length === 0) errors.push('At least one item is required')
    
    return {
      valid: errors.length === 0,
      errors
    }
  }

  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }
}