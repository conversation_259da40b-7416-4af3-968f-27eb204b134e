{"name": "macedon-ranges-voice-agent", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "setup:agent": "node scripts/setup-elevenlabs-agent.js", "setup:env": "cp .env.local.example .env.local"}, "dependencies": {"@copilotkit/backend": "^0.37.0", "@copilotkit/react-core": "^1.0.0", "@copilotkit/react-ui": "^1.0.0", "@copilotkit/shared": "^1.0.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.350.0", "next": "14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "dotenv": "^16.4.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}, "trustedDependencies": ["unrs-resolver"]}